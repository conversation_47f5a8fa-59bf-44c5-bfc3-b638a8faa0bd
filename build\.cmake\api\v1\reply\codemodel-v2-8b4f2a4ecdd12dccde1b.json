{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "DarkGameServer", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-e6253d5e13a228522c56.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameGateway::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameGateway-Debug-227b8ee087fcd3b37245.json", "name": "DarkGameGateway", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameServer::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameServer-Debug-64f479a84635f4e5e339.json", "name": "DarkGameServer", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-d5ca0772c7084ddb346c.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "DarkGameServer", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-e6253d5e13a228522c56.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameGateway::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameGateway-Release-c0f7bedc695f9ecd343f.json", "name": "DarkGameGateway", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameServer::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameServer-Release-090875cae52c5a1da124.json", "name": "DarkGameServer", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-d5ca0772c7084ddb346c.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "DarkGameServer", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-e6253d5e13a228522c56.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameGateway::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameGateway-MinSizeRel-a670590bf887141fa60f.json", "name": "DarkGameGateway", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameServer::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameServer-MinSizeRel-c7dd64179ab0800b74c3.json", "name": "DarkGameServer", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-d5ca0772c7084ddb346c.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "DarkGameServer", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-e6253d5e13a228522c56.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameGateway::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameGateway-RelWithDebInfo-3ec5fecf3a07cac60f1b.json", "name": "DarkGameGateway", "projectIndex": 0}, {"directoryIndex": 0, "id": "DarkGameServer::@6890427a1f51a3e7e1df", "jsonFile": "target-DarkGameServer-RelWithDebInfo-08e9be7e09098284bde7.json", "name": "DarkGameServer", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-d5ca0772c7084ddb346c.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/MyProject/server/build", "source": "E:/MyProject/server"}, "version": {"major": 2, "minor": 8}}