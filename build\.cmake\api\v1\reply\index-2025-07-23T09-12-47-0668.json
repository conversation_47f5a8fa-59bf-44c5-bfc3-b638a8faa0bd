{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "E:/CMake/bin/cmake.exe", "cpack": "E:/CMake/bin/cpack.exe", "ctest": "E:/CMake/bin/ctest.exe", "root": "E:/CMake/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 0, "string": "4.1.0-rc2", "suffix": "rc2"}}, "objects": [{"jsonFile": "codemodel-v2-8b4f2a4ecdd12dccde1b.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-3c629343ca9b6a754937.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-176250beacace7e2c50b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-5cc70694bf8def77444c.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-3c629343ca9b6a754937.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-8b4f2a4ecdd12dccde1b.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-5cc70694bf8def77444c.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-176250beacace7e2c50b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}